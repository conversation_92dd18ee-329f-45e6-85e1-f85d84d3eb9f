import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { openInAppBrowser } from '../../utils/inAppBrowser';
import { motion, AnimatePresence } from 'framer-motion';
import * as Tone from 'tone';
import { getFirebase } from './firebase';
import useGoogleMaps from '../../hooks/useGoogleMaps';

// Add this ErrorBoundary component at the top of the file
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Codex Error Boundary caught an error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="bg-white h-full flex items-center justify-center p-4">
                    <div className="text-center">
                        <h2 className="text-xl font-bold text-red-600 mb-4">Something went wrong</h2>
                        <p className="text-gray-600 mb-4">The codex encountered an error.</p>
                        <button 
                            onClick={() => {
                                this.setState({ hasError: false, error: null });
                                window.location.reload();
                            }}
                            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                        >
                            Reload Codex
                        </button>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

// --- Helper Components & Icons ---

const LockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400 w-full h-full">
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
);

const PlayIcon = () => <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg>;
const PauseIcon = () => <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path></svg>;

// --- Modal Components ---

function NodeDetailModal({ node, onUnlock, onClose }) {
    const handleContentClick = (e) => {
        if (e.target.tagName === 'A' && e.target.href) {
            e.preventDefault();
            openInAppBrowser(e.target.href);
        }
    };

    return (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <div className="bg-slate-100 rounded-2xl shadow-2xl border border-slate-300 w-full max-w-lg max-h-[90vh] overflow-y-auto flex flex-col" onClick={(e) => e.stopPropagation()}>
                <div className="p-6 pt-0 text-left">
                    {node.image && <img src={node.image} alt={node.name} className="w-full h-40 object-cover rounded-lg my-4"/>}
                    <h2 className="text-2xl font-bold text-slate-800">{node.name}</h2>
                </div>

                <div className="flex-1 px-6" onClick={handleContentClick}>
                    <div className="text-left text-slate-700 text-lg leading-relaxed my-4" dangerouslySetInnerHTML={{ __html: node.info_text }}></div>

                    {node.location && (
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                if (window.navigateToMap) {
                                    window.navigateToMap(node.location.latitude, node.location.longitude);
                                } else {
                                    console.error("navigateToMap function not found on window object.");
                                    // Fallback to Google Maps if the main app function isn't available
                                    window.open(`https://www.google.com/maps?q=${node.location.latitude},${node.location.longitude}`, '_blank');
                                }
                            }}
                            className="inline-flex items-center gap-2 bg-sky-500 text-white font-medium text-lg py-3 px-4 rounded-lg hover:bg-sky-600 transition-colors duration-300 mb-4"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            View on Map
                        </button>
                    )}

                    {node.unlocked && <p className="text-green-600 text-xl my-4 font-bold">UNLOCKED</p>}
                </div>

                <div className="p-6 pt-0">
                    {!node.unlocked && <button onClick={() => onUnlock(node)} className="w-full bg-sky-500 text-white font-bold py-3 px-6 rounded-lg hover:bg-sky-600 transition-colors duration-300 transform hover:scale-105">I Am Here - Unlock Node</button>}
                    <button onClick={onClose} className="w-full mt-3 bg-slate-600 text-white font-medium py-2 px-6 rounded-lg hover:bg-slate-700 transition-colors duration-300">Close</button>
                </div>
            </div>
        </div>
    );
}

function CouponModal({ reward, user, journeyId, unlockedCount, onClose }) {
    const [isRedeemed, setIsRedeemed] = useState(false);
    const [redeemedAt, setRedeemedAt] = useState(null);
    const couponId = `${journeyId}_${unlockedCount}`;

    useEffect(() => {
        const checkRedemptionStatus = async () => {
            if (!user) return;
            try {
                const { db, doc, getDoc } = await getFirebase();
                const couponDocRef = doc(db, `users/${user.uid}/redeemed_coupons`, couponId);
                const docSnap = await getDoc(couponDocRef);
                if (docSnap.exists()) {
                    setIsRedeemed(true);
                    setRedeemedAt(docSnap.data().redeemed_at.toDate());
                }
            } catch (error) {
                console.error("Error checking coupon status:", error);
            }
        };
        checkRedemptionStatus();
    }, [user, couponId]);

    const handleRedeem = async () => {
        if (!user) return;
        const isConfirmed = window.confirm("Are you sure you want to redeem this coupon? This action cannot be undone.");
        if (isConfirmed) {
            try {
                const { db, doc, setDoc, serverTimestamp } = await getFirebase();
                const couponDocRef = doc(db, `users/${user.uid}/redeemed_coupons`, couponId);
                const redemptionTime = new Date();
                await setDoc(couponDocRef, { redeemed_at: serverTimestamp() });
                setIsRedeemed(true);
                setRedeemedAt(redemptionTime);
            } catch (error) {
                console.error("Error redeeming coupon:", error);
                alert("Failed to redeem coupon. Please try again.");
            }
        }
    };

    return (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[60] p-4" onClick={onClose}>
            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm p-6 text-center relative" onClick={(e) => e.stopPropagation()}>
                <h2 className="text-2xl font-bold text-gray-800">{reward.title}</h2>
                <p className="text-gray-600 mt-2 mb-6">{reward.message}</p>
                
                <div className={`border-2 border-dashed rounded-lg p-8 transition-all duration-300 ${isRedeemed ? 'border-gray-300 bg-gray-100' : 'border-cyan-500 bg-cyan-50'}`}>
                    {isRedeemed ? (
                        <div>
                            <h3 className="text-4xl font-bold text-gray-400 uppercase tracking-widest">Redeemed</h3>
                            {redeemedAt && <p className="text-gray-500 mt-2">on {redeemedAt.toLocaleString()}</p>}
                        </div>
                    ) : (
                        <button onClick={handleRedeem} className="w-full bg-cyan-500 text-white font-bold py-4 px-6 rounded-lg hover:bg-cyan-600 transition-colors transform hover:scale-105">
                            Present this coupon at the bar
                        </button>
                    )}
                </div>

                <p className="text-sm text-gray-500 mt-4">Staff: click to redeem drink</p>

                <button onClick={onClose} className="w-full mt-6 bg-gray-200 text-gray-700 font-medium py-2 px-6 rounded-lg hover:bg-gray-300 transition-colors">Close</button>
            </div>
        </div>
    );
}

function RewardModal({ reward, onClose, onOpenCoupon }) {
    const audioRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const togglePlayPause = () => {
        try {
            if (audioRef.current) {
                if (isPlaying) {
                    audioRef.current.pause();
                } else {
                    audioRef.current.play();
                }
                setIsPlaying(!isPlaying);
            }
        } catch (error) {
            console.error('Audio playback error:', error);
            setIsPlaying(false);
        }
    };


    useEffect(() => {
        const audioEl = audioRef.current;
        const handleEnded = () => setIsPlaying(false);
        const handleEscape = (event) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        if (audioEl) {
            audioEl.addEventListener('ended', handleEnded);
        }
        document.addEventListener('keydown', handleEscape);

        return () => {
            if (audioEl) {
                audioEl.pause();
                audioEl.removeEventListener('ended', handleEnded);
            }
            document.removeEventListener('keydown', handleEscape);
        };
    }, []);

    return (
        <div
            className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
            onClick={(e) => {
                e.stopPropagation();
                onClose();
            }}
        >
            <div
                className="bg-gray-800 rounded-2xl shadow-2xl border border-cyan-500/50 w-full max-w-md p-8 text-center relative"
                onClick={(e) => e.stopPropagation()}
            >
                {/* Close button */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
                    aria-label="Close"
                >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                {reward?.imageSrc && <img src={reward.imageSrc} alt="Codex Revealed" className="w-48 h-48 mx-auto rounded-lg mb-6 shadow-lg" />}
                <h2 className="text-3xl font-bold text-cyan-300">{reward?.title || 'Reward Unlocked!'}</h2>
                <p className="text-gray-300 mt-4 mb-8 text-lg">{reward?.message || 'Congratulations on your progress!'}</p>
                
                {reward.audioSrc && (
                    <div className="flex items-center justify-center space-x-4 bg-black/20 p-3 rounded-lg mb-6">
                        <audio ref={audioRef} src={reward.audioSrc} onPlay={() => setIsPlaying(true)} onPause={() => setIsPlaying(false)}></audio>
                        <button onClick={togglePlayPause} className="text-cyan-300 hover:text-white transition-colors">
                            {isPlaying ? <PauseIcon /> : <PlayIcon />}
                        </button>
                        <p className="text-gray-300 font-medium">Play Meditation</p>
                    </div>
                )}
                
                {reward.downloadSrc && (
                    <div className="text-left bg-black/20 p-3 rounded-lg mb-6">
                        <h3 className="font-bold text-white mb-2">Download Files</h3>
                        <a href={reward.downloadSrc} download className="text-cyan-400 hover:underline">
                            File Name: {reward.downloadSrc.split('/').pop()}
                        </a>
                        <p className="text-gray-400 text-sm">File Size: N/A</p>
                    </div>
                )}

                {reward.voucherLink === 'COUPON' && (
                    <button onClick={onOpenCoupon} className="block w-full bg-yellow-500 text-gray-900 font-bold py-3 px-8 rounded-lg hover:bg-yellow-400 transition-colors duration-300">
                        View Coupon
                    </button>
                )}

                {reward.voucherLink && reward.voucherLink.startsWith('http') && (
                     <button onClick={() => openInAppBrowser(reward.voucherLink)} className="block w-full bg-yellow-500 text-gray-900 font-bold py-3 px-8 rounded-lg hover:bg-yellow-400 transition-colors duration-300">View Rewards</button>
                )}
                
                <button onClick={onClose} className="w-full mt-4 bg-gray-700 text-gray-300 font-medium py-2 px-6 rounded-lg hover:bg-gray-600 transition-colors duration-300">Continue Journey</button>
            </div>
        </div>
    );
}

function RevealModal({ node, codexImage }) {
    if (!node) return null;

    return (
        <div
            className="fixed inset-0 bg-black/70 flex flex-col items-center justify-center z-50 p-4 pointer-events-none"
        >
            <div
                className="relative"
            >
                <img
                    src={codexImage}
                    alt="Revealed Piece"
                    className="w-[min(80vw,400px)] h-[min(80vw,400px)]"
                    style={{ clipPath: node.clipPath }}
                />
            </div>
            <p 
                className="text-white text-center text-xl font-bold mt-4 bg-black/50 rounded-lg px-4 py-2">
                {node.name} Unlocked!
            </p>
        </div>
    );
}
// --- Mystery Journey Component ---

function MysteryJourney({ journeyId, user, onBack, title, description, backgroundImage }) {
    const { isLoaded: isMapsLoaded, error: mapsError } = useGoogleMaps();
    const [status, setStatus] = useState('idle'); // 'idle', 'loading', 'ready', 'error'
    const [waypoints, setWaypoints] = useState([]);
    const [currentWaypointIndex, setCurrentWaypointIndex] = useState(0);
    const [destination, setDestination] = useState(null);
    const [destinationName, setDestinationName] = useState('');
    const [destinationClue, setDestinationClue] = useState('');
    const [destinationDistance, setDestinationDistance] = useState('');
    const [destinationRevealed, setDestinationRevealed] = useState(false);
    const [travelMode, setTravelMode] = useState('DRIVING'); // Default to driving
    const [feedbackMessage, setFeedbackMessage] = useState('');
    const [isCheckingLocation, setIsCheckingLocation] = useState(false);
    const [lastPosition, setLastPosition] = useState(null);

    const mapRef = useRef(null);
    const [map, setMap] = useState(null);
    const [directionsResponse, setDirectionsResponse] = useState(null);
    const visiblePolylineRef = useRef(null);
    const userMarkerRef = useRef(null);

    // Dynamic distance calculations based on travel mode
    const getRouteDistance = () => {
        return travelMode === 'WALKING' ? 300 : 800; // 300m for walking, 800m for driving
    };

    const getUnlockDistance = () => {
        return travelMode === 'WALKING' ? 20 : 80; // 20m for walking, 80m for driving
    };

    // Add function to calculate bearing between two points
    const calculateBearing = (lat1, lng1, lat2, lng2) => {
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const lat1Rad = lat1 * Math.PI / 180;
        const lat2Rad = lat2 * Math.PI / 180;
        
        const y = Math.sin(dLng) * Math.cos(lat2Rad);
        const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);
        
        return (Math.atan2(y, x) * 180 / Math.PI + 360) % 360;
    };

    const showFeedback = (message, duration = 6000) => {
        setFeedbackMessage(message);
        setTimeout(() => setFeedbackMessage(''), duration);
    };

    const startJourney = () => {
        setStatus('loading');
    };

    // Step 1: Fetch data when journey starts
    useEffect(() => {
        if (status !== 'loading' || !journeyId || !user) return;

        const fetchData = async () => {
            try {
                const { db, collection, getDocs, doc, getDoc } = await getFirebase();
                const journeyDocRef = doc(db, 'codexes', journeyId);
                const journeyDocSnap = await getDoc(journeyDocRef);

                if (!journeyDocSnap.exists()) throw new Error("Journey not found.");

                const journeyData = journeyDocSnap.data();
                setDestination(journeyData.destination);
                setDestinationName(journeyData.destination_name);
                setDestinationClue(journeyData.destination_clue);
                setDestinationDistance(journeyData.destination_distance);
                setTravelMode(journeyData.travel_mode || 'DRIVING');
                console.log('Travel mode set to:', journeyData.travel_mode || 'DRIVING');

                const waypointsCollectionRef = collection(journeyDocRef, 'waypoints');
                const waypointsSnapshot = await getDocs(waypointsCollectionRef);
                const fetchedWaypoints = waypointsSnapshot.docs
                    .map(d => ({ id: d.id, ...d.data() }))
                    .sort((a, b) => a.order - b.order);
                setWaypoints(fetchedWaypoints);

                const progressDocRef = doc(db, `users/${user.uid}/journey_progress`, journeyId);
                const progressSnap = await getDoc(progressDocRef);
                if (progressSnap.exists()) {
                    const progressData = progressSnap.data();
                    setCurrentWaypointIndex(progressData.currentWaypointIndex || 0);
                    setDestinationRevealed(progressData.destinationRevealed || false);
                }
                setStatus('ready'); // Data is ready, proceed to map init
            } catch (error) {
                console.error("Error fetching journey data:", error);
                showFeedback(error.message || "Could not load journey data.");
                setStatus('error');
            }
        };

        fetchData();
    }, [status, journeyId, user]);

    // Step 2: Initialize map when data and maps API are ready
    useEffect(() => {
        if (status !== 'ready' || !mapRef.current || map || !isMapsLoaded) return;

        const initMap = async () => {
            const { Map } = await google.maps.importLibrary("maps");
            const newMap = new Map(mapRef.current, {
                center: { lat: waypoints[0].location.latitude, lng: waypoints[0].location.longitude },
                zoom: 15,
                mapId: '9a6789cf3ff1289e',
                disableDefaultUI: true,
            });
            setMap(newMap);
        };

        if (isMapsLoaded) {
            initMap();
        }
    }, [status, map, waypoints, isMapsLoaded]);

    // Step 3: Fetch directions when map is ready
    useEffect(() => {
        if (!map || !waypoints.length || !destination) return;

        const fetchDirections = async () => {
            try {
                const directionsService = new window.google.maps.DirectionsService();
                directionsService.route(
                    {
                        origin: new window.google.maps.LatLng(waypoints[0].location.latitude, waypoints[0].location.longitude),
                        destination: new window.google.maps.LatLng(destination.latitude, destination.longitude),
                        waypoints: waypoints.slice(1).map(wp => ({ location: new window.google.maps.LatLng(wp.location.latitude, wp.location.longitude), stopover: true })),
                        travelMode: window.google.maps.TravelMode[travelMode],
                    },
                    (result, status) => {
                        console.log('Directions API response:', status, result);
                        if (status === window.google.maps.DirectionsStatus.OK) {
                            console.log('Directions loaded successfully:', result);
                            setDirectionsResponse(result);
                        } else {
                            console.error(`Error fetching directions: ${status}`);
                            showFeedback(`Could not calculate the route. Status: ${status}`);
                        }
                    }
                );
            } catch (e) {
                console.error("Failed to load Directions Service", e);
                showFeedback("Could not load routing service.");
            }
        };

        fetchDirections();
    }, [map, waypoints, destination]);

    const saveProgress = useCallback(async (index, revealed) => {
        if (!user || !journeyId) return;
        try {
            const { db, doc, setDoc } = await getFirebase();
            const progressDocRef = doc(db, `users/${user.uid}/journey_progress`, journeyId);
            await setDoc(progressDocRef, {
                currentWaypointIndex: index,
                destinationRevealed: revealed
            }, { merge: true });
        } catch (error) {
            console.error("Error saving progress:", error);
        }
    }, [user, journeyId]);

    const updateRouteDisplay = (userLatLng) => {
        console.log('updateRouteDisplay called:', { directionsResponse, map, userLatLng, currentWaypointIndex });
        if (!directionsResponse || !map) return;

        // Clean up previous route display - ensure it's properly cleared
        if (visiblePolylineRef.current) {
            visiblePolylineRef.current.setMap(null);
        }

        // Show route only to the next waypoint, not the entire route
        const currentWaypoint = waypoints[currentWaypointIndex];
        if (!currentWaypoint) return;

        // Create a route from user's current location to the next waypoint
        const directionsService = new window.google.maps.DirectionsService();
        directionsService.route(
            {
                origin: userLatLng,
                destination: new window.google.maps.LatLng(currentWaypoint.location.latitude, currentWaypoint.location.longitude),
                travelMode: window.google.maps.TravelMode[travelMode],
            },
            (result, status) => {
                if (status === window.google.maps.DirectionsStatus.OK) {
                    // Get the route path and show only next segment based on travel mode
                    const routePath = result.routes[0].overview_path;

                    // Show next segment of route from user's current position based on travel mode
                    const visiblePath = [userLatLng]; // Start with user's position
                    let totalDistance = 0;

                    for (let i = 0; i < routePath.length - 1; i++) {
                        const segmentDistance = window.google.maps.geometry.spherical.computeDistanceBetween(routePath[i], routePath[i + 1]);

                        if (totalDistance + segmentDistance <= getRouteDistance()) {
                            // Add the entire segment
                            visiblePath.push(routePath[i + 1]);
                            totalDistance += segmentDistance;
                        } else {
                            // Add partial segment to reach exactly the route distance limit
                            const remainingDistance = getRouteDistance() - totalDistance;
                            const ratio = remainingDistance / segmentDistance;
                            const partialPoint = window.google.maps.geometry.spherical.interpolate(routePath[i], routePath[i + 1], ratio);
                            visiblePath.push(partialPoint);
                            break;
                        }
                    }

                    // Create polyline for the visible route segment
                    visiblePolylineRef.current = new window.google.maps.Polyline({
                        path: visiblePath,
                        geodesic: true,
                        strokeColor: '#FF6B35',  // Changed to orange/red for better visibility
                        strokeOpacity: 1.0,      // Increased opacity
                        strokeWeight: 6,         // Increased thickness
                        map: map,
                    });

                    console.log(`Route segment displayed: ${Math.round(totalDistance)}m to next waypoint (${travelMode} mode)`);
                } else {
                    console.error('Failed to get route to next waypoint:', status);
                }
            }
        );
    };
    const checkLocation = useCallback(() => {
        if (!navigator.geolocation || !map || !directionsResponse || isCheckingLocation) {
            return; // Skip if already checking
        }
        setIsCheckingLocation(true);

        navigator.geolocation.getCurrentPosition(async (position) => {
            const userLatLng = new window.google.maps.LatLng(position.coords.latitude, position.coords.longitude);
            
            if (lastPosition) {
                const heading = calculateBearing(lastPosition.lat(), lastPosition.lng(), userLatLng.lat(), userLatLng.lng());
                map.setHeading(heading);
                map.setTilt(45);
            }
            setLastPosition(userLatLng);

            if (userMarkerRef.current) {
                userMarkerRef.current.position = userLatLng;
            } else {
                const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");
                const userMarkerElement = document.createElement('div');
                userMarkerElement.style.cssText = 'width: 18px; height: 18px; border-radius: 50%; background-color: #e00874ff; border: 3px solid white; box-shadow: 0 0 6px rgba(0,0,0,0.4);';
                userMarkerRef.current = new AdvancedMarkerElement({ position: userLatLng, map: map, title: "Your Location", content: userMarkerElement });
            }

            map.setCenter(userLatLng);

            if (destinationRevealed) {
                if (visiblePolylineRef.current) visiblePolylineRef.current.setMap(null);
                const renderer = new window.google.maps.DirectionsRenderer();
                renderer.setMap(map);
                renderer.setDirections(directionsResponse);
                setIsCheckingLocation(false);
                return;
            }

            const allWaypointsCompleted = currentWaypointIndex >= waypoints.length;

            if (allWaypointsCompleted) {
                // STAGE 2: Navigate to the final destination
                const directionsService = new window.google.maps.DirectionsService();
                directionsService.route({
                    origin: userLatLng,
                    destination: new window.google.maps.LatLng(destination.latitude, destination.longitude),
                    travelMode: window.google.maps.TravelMode[travelMode],
                }, (result, status) => {
                    if (status === window.google.maps.DirectionsStatus.OK) {
                        if (visiblePolylineRef.current) {
                            visiblePolylineRef.current.setMap(null);
                        }
                        
                        const routePath = result.routes[0].overview_path;
                        const visiblePath = [userLatLng];
                        let totalDistance = 0;
                        for (let i = 0; i < routePath.length - 1; i++) {
                            const segmentDistance = window.google.maps.geometry.spherical.computeDistanceBetween(routePath[i], routePath[i + 1]);
                            if (totalDistance + segmentDistance <= getRouteDistance()) {
                                visiblePath.push(routePath[i + 1]);
                                totalDistance += segmentDistance;
                            } else {
                                const remainingDistance = getRouteDistance() - totalDistance;
                                const ratio = remainingDistance / segmentDistance;
                                const partialPoint = window.google.maps.geometry.spherical.interpolate(routePath[i], routePath[i + 1], ratio);
                                visiblePath.push(partialPoint);
                                break;
                            }
                        }
                        visiblePolylineRef.current = new window.google.maps.Polyline({ path: visiblePath, geodesic: true, strokeColor: '#FF6B35', strokeOpacity: 1.0, strokeWeight: 6, map: map });
                    }
                });

                const distToDest = window.google.maps.geometry.spherical.computeDistanceBetween(userLatLng, new window.google.maps.LatLng(destination.latitude, destination.longitude));
                console.log(`Distance to destination: ${Math.round(distToDest)}m (threshold: ${getUnlockDistance()}m, mode: ${travelMode})`);
                if (distToDest < getUnlockDistance()) { // Dynamic threshold based on travel mode
                    setDestinationRevealed(true);
                    saveProgress(currentWaypointIndex, true);
                    showFeedback("You've reached the final area! The destination is revealed!", 8000);
                    if (visiblePolylineRef.current) {
                        visiblePolylineRef.current.setMap(null);
                    }
                    const renderer = new window.google.maps.DirectionsRenderer();
                    renderer.setMap(map);
                    renderer.setDirections(directionsResponse);
                    const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");
                    new AdvancedMarkerElement({ position: new window.google.maps.LatLng(destination.latitude, destination.longitude), map: map, title: destinationName });
                }
            } else {
                // STAGE 1: Navigate between waypoints
                updateRouteDisplay(userLatLng);

                const currentWaypoint = waypoints[currentWaypointIndex];
                if (currentWaypoint) {
                    const distanceToWaypoint = window.google.maps.geometry.spherical.computeDistanceBetween(userLatLng, new window.google.maps.LatLng(currentWaypoint.location.latitude, currentWaypoint.location.longitude));
                    console.log(`Distance to waypoint: ${Math.round(distanceToWaypoint)}m (threshold: ${getUnlockDistance()}m, mode: ${travelMode})`);
                    if (distanceToWaypoint < getUnlockDistance()) { // Dynamic threshold based on travel mode
                        const locationName = currentWaypoint.name || `Location ${currentWaypointIndex + 1}`;
                        const nextIndex = currentWaypointIndex + 1;
                        
                        setCurrentWaypointIndex(nextIndex);
                        saveProgress(nextIndex, false);

                        if (visiblePolylineRef.current) {
                            visiblePolylineRef.current.setMap(null);
                        }

                        if (nextIndex < waypoints.length) {
                            showFeedback(`You've arrived at ${locationName}! The next location is now available.`, 10000);
                        } else {
                            showFeedback(`You've completed all locations! Now head to the final destination.`, 10000);
                        }
                    }
                }
            }
            setIsCheckingLocation(false);
        }, (error) => {
            console.error("Error getting location:", error);
            showFeedback("Could not get your location. Please enable location services.", 5000);
            setIsCheckingLocation(false);
        }, {
            enableHighAccuracy: true,
            timeout: 8000,
            maximumAge: 5000
        });
    }, [map, directionsResponse, destinationRevealed, waypoints, currentWaypointIndex, destination, destinationName, saveProgress, travelMode]);

    // Step 4: Start location tracking when directions are ready
    useEffect(() => {
        if (status === 'ready' && !destinationRevealed && directionsResponse) {
            checkLocation(); // Initial check
            const intervalId = setInterval(checkLocation, 3000); // Check every 3 seconds
            return () => clearInterval(intervalId);
        }
    }, [status, destinationRevealed, directionsResponse, checkLocation]);

    const renderContent = () => {
        if (status === 'idle') {
            return (
                <div className="w-full max-w-3xl mx-auto text-center px-4 pb-8 relative">
                    {backgroundImage && <img src={backgroundImage} alt="Journey background" className="absolute inset-0 w-full h-full object-cover opacity-10 -z-10" />}
                    <button onClick={onBack} className="absolute top-4 left-0 bg-gray-200 text-gray-700 p-2 rounded-full hover:bg-gray-300 transition-colors z-10">
                        &larr; Back
                    </button>
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-800 tracking-wider mt-16 mb-4">{title || 'A New Adventure Awaits'}</h2>
                    {description && <div className="text-base md:text-lg text-gray-700 leading-relaxed my-4" dangerouslySetInnerHTML={{ __html: description }}></div>}
                    <button onClick={startJourney} className="mt-6 bg-sky-500 text-white font-bold py-3 px-6 rounded-lg hover:bg-sky-600 transition-all duration-300 transform hover:scale-105">
                        Start Your Mystery Journey
                    </button>
                </div>
            );
        }

        if (status === 'loading') {
            return <div className="bg-white h-full flex items-center justify-center"><div className="text-gray-600">Loading Mystery Journey...</div></div>;
        }

        if (status === 'error') {
            return <div className="bg-white h-full flex items-center justify-center"><div className="text-red-600">Failed to load journey. Please try again.</div></div>;
        }

        if (status === 'ready') {
            return (
                <div className="w-full max-w-3xl mx-auto text-center px-4 pb-8 relative">
                    {backgroundImage && <img src={backgroundImage} alt="Journey background" className="absolute inset-0 w-full h-full object-cover opacity-10 -z-10" />}
                    <button onClick={onBack} className="absolute top-4 left-0 bg-gray-200 text-gray-700 p-2 rounded-full hover:bg-gray-300 transition-colors z-10">
                        &larr; Back
                    </button>
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-800 tracking-wider mt-16 mb-4">{title || 'A New Adventure Awaits'}</h2>
                    
                    <div ref={mapRef} className="w-full h-96 bg-gray-200 rounded-lg shadow-md my-4"></div>

                    {destinationRevealed ? (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="mt-6 p-8 bg-yellow-400 rounded-2xl shadow-2xl border-2 border-white/50 text-center"
                        >
                            <div className="w-16 h-16 mx-auto bg-white/30 rounded-full flex items-center justify-center mb-4 overflow-hidden">
                                <img src="/icons/icon-192.png" alt="Journey complete" className="w-10 h-10 object-contain" />
                            </div>
                            <h3 className="text-3xl font-bold text-white drop-shadow-md">Congratulations! Your journey is complete at {destinationName}</h3>
                        </motion.div>
                    ) : (
                        <div>
                            {waypoints.length > 0 && currentWaypointIndex < waypoints.length && (
                                <div className="mt-6 p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
                                    <p className="text-lg text-gray-600 mb-2">Location {currentWaypointIndex + 1} of {waypoints.length}</p>
                                    <p className="text-xl text-gray-800 leading-relaxed">{waypoints[currentWaypointIndex].prompt}
                                        {waypoints[currentWaypointIndex].distance_to_next && (
                                            <span className="text-gray-500 text-sm ml-2">({waypoints[currentWaypointIndex].distance_to_next})</span>
                                        )}
                                    </p>
                                    <p className="text-md text-gray-500 mt-4">Your route is being revealed incrementally. Keep moving!</p>
                                </div>
                            )}
                            {waypoints.length > 0 && currentWaypointIndex >= waypoints.length && !destinationRevealed && (
                                <div className="mt-6 p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
                                    <p className="text-lg text-gray-600 mb-2">Final Destination</p>
                                    <p className="text-xl text-gray-800 leading-relaxed">{destinationClue}
                                        {destinationDistance &&
                                            <span className="text-gray-500 text-sm ml-2">({destinationDistance})</span>
                                        }
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            );
        }
        return null;
    };

    return (
        <div className="bg-gray-50 text-gray-900 h-full font-sans flex flex-col items-center p-4 overflow-y-auto min-h-full">
            {renderContent()}
            <AnimatePresence>
                {feedbackMessage && (
                    <motion.div
                        initial={{ opacity: 0, y: 50 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 20 }}
                        className="fixed bottom-10 left-1/2 -translate-x-1/2 bg-blue-600 text-white py-4 px-8 rounded-xl shadow-2xl z-50 max-w-lg mx-4 text-center border border-blue-500"
                    >
                        <div className="text-lg font-semibold leading-relaxed">
                            {feedbackMessage}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}

// --- Theme Journey Component ---

function ThemeJourney({ journeyId, user, title, description, backgroundImage, onBack }) {
    const [nodes, setNodes] = useState([]);
    const [codexData, setCodexData] = useState(null);
    const [selectedNode, setSelectedNode] = useState(null);
    const [reward, setReward] = useState(null);
    const [activeModal, setActiveModal] = useState(null);
    const [revealedNode, setRevealedNode] = useState(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
    const [synth, setSynth] = useState(null);
    
    // Theme journeys use a fixed unlock distance (no travel mode concept)
    const getThemeUnlockDistance = () => 100; // 100 meters for theme journeys
    
    // Remove the useMemo synth creation - we'll create it on demand
    
    // Add cleanup effect
    useEffect(() => {
        return () => {
            // Clean up any timeouts or intervals
            setSelectedNode(null);
            setReward(null);
            setActiveModal(null);
            setRevealedNode(null);
            if (synth) {
                try {
                    synth.dispose();
                } catch (e) {
                    // Ignore disposal errors
                }
            }
        };
    }, [synth]);

    const generateClipPaths = (numNodes) => {
        if (numNodes === 0) return [];
        const paths = [];
        const anglePerSlice = 360 / numNodes;

        for (let i = 0; i < numNodes; i++) {
            const startAngleDeg = i * anglePerSlice - 90;
            
            const points = ['50% 50%'];
            // Use a dynamic number of segments for the arc for smoothness
            const segments = Math.max(5, Math.floor(anglePerSlice / 10));

            for (let j = 0; j <= segments; j++) {
                const angle = startAngleDeg + (anglePerSlice / segments) * j;
                const angleRad = angle * (Math.PI / 180);
                const x = 50 + 50 * Math.cos(angleRad);
                const y = 50 + 50 * Math.sin(angleRad);
                points.push(`${x}% ${y}%`);
            }
            paths.push(`polygon(${points.join(', ')})`);
        }
        return paths;
    };

    const fetchCodexData = async (currentJourneyId, userId) => {
        try {
            const { db, collection, getDocs, doc, getDoc } = await getFirebase();

            const codexDocRef = doc(db, `codexes/${currentJourneyId}`);
            const codexDocSnap = await getDoc(codexDocRef);
            const fetchedCodexData = codexDocSnap.exists() ? codexDocSnap.data() : null;
            setCodexData(fetchedCodexData);

            const nodesCollectionRef = collection(db, `codexes/${currentJourneyId}/nodes`);
            const nodesSnapshot = await getDocs(nodesCollectionRef);
            const allNodes = nodesSnapshot.docs.map((doc, index) => ({
                id: doc.id,
                ...doc.data(),
                clipPath: generateClipPaths(nodesSnapshot.docs.length)[index]
            }));

            const userDocRef = doc(db, `users/${userId}`);
            const userDocSnap = await getDoc(userDocRef);
            const unlockedNodeIds = userDocSnap.exists() ? userDocSnap.data().unlocked_nodes || [] : [];

            const mergedNodes = allNodes.map(node => ({
                ...node,
                unlocked: unlockedNodeIds.includes(node.id)
            }));

            setNodes(mergedNodes);
        } catch (error) {
            console.error("Error fetching codex data:", error);
        } finally {
            setIsInitialized(true);
        }
    };

    useEffect(() => {
        if (journeyId && user) {
            fetchCodexData(journeyId, user.uid);
        }
    }, [journeyId, user]);

    const unlockedCount = useMemo(() => nodes.filter(n => n.unlocked).length, [nodes]);

    // Add reward logic for 4 and 8 node unlocks
    useEffect(() => {
        if (codexData?.rewards && codexData.rewards[unlockedCount] && unlockedCount > 0) {
            setTimeout(() => {
                // Get download files from unlocked nodes
                const unlockedNodes = nodes.filter(n => n.unlocked);
                const downloadFiles = unlockedNodes
                    .filter(n => n.downloadSrc)
                    .map(n => n.downloadSrc)
                    .filter(Boolean);
                
                const rewardWithDownloads = {
                    ...codexData.rewards[unlockedCount],
                    downloadSrc: downloadFiles.length > 0 ? downloadFiles[0] : codexData.rewards[unlockedCount].downloadSrc
                };
                
                setReward(rewardWithDownloads);
                setActiveModal('REWARD');
            }, 2600);
        }
    }, [unlockedCount, codexData, nodes]);

    const handleUnlockNode = useCallback(async (nodeId) => {
        if (!user) return;
        const nodeToUnlock = nodes.find(n => n.id === nodeId);
        
        try {
            const { db, doc, setDoc, arrayUnion } = await getFirebase();
            const userDocRef = doc(db, `users/${user.uid}`);
            await setDoc(userDocRef, { unlocked_nodes: arrayUnion(nodeId) }, { merge: true });
            
            const updatedNodes = nodes.map(n => n.id === nodeId ? { ...n, unlocked: true } : n);
            setNodes(updatedNodes);
            setSelectedNode(null);
            setRevealedNode(nodeToUnlock);
            setTimeout(() => setRevealedNode(null), 2500);
        } catch (error) {
            console.error("Error unlocking node:", error);
        }
    }, [nodes, user]);

    const handleLocationUnlock = useCallback(async (node) => {
        if (!user) return;
        
        // Check if geolocation is available
        if (!navigator.geolocation) {
            alert('Geolocation is not supported by this browser.');
            return;
        }

        try {
            // Get current position
            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 10000
                });
            });

            const userLat = position.coords.latitude;
            const userLng = position.coords.longitude;
            const nodeLat = node.location.latitude;
            const nodeLng = node.location.longitude;

            // Calculate distance using Haversine formula
            const R = 6371e3; // Earth's radius in meters
            const φ1 = userLat * Math.PI/180;
            const φ2 = nodeLat * Math.PI/180;
            const Δφ = (nodeLat-userLat) * Math.PI/180;
            const Δλ = (nodeLng-userLng) * Math.PI/180;

            const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                      Math.cos(φ1) * Math.cos(φ2) *
                      Math.sin(Δλ/2) * Math.sin(Δλ/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distance = R * c; // Distance in meters

            console.log(`Distance to node: ${Math.round(distance)}m`);

            if (distance <= getThemeUnlockDistance()) { // Fixed threshold for theme journeys
                // Play sound and unlock
                try {
                    if (Tone.context.state !== 'running') {
                        await Tone.start();
                    }
                    
                    if (!synth) {
                        const newSynth = new Tone.MembraneSynth().toDestination();
                        setSynth(newSynth);
                        if (node.tone) {
                            newSynth.triggerAttackRelease(node.tone, "8n");
                        }
                    } else if (node.tone) {
                        synth.triggerAttackRelease(node.tone, "8n");
                    }
                } catch (error) {
                    console.warn('Failed to play tone:', error);
                }
                
                handleUnlockNode(node.id);
            } else {
                alert(`You need to be within ${getThemeUnlockDistance()} meters of this location to unlock it. You are currently ${Math.round(distance)} meters away.`);
            }

        } catch (error) {
            console.error("Error getting location:", error);
            let errorMessage = 'Could not get your location. ';
            if (error.code === 1) { // PERMISSION_DENIED
                errorMessage += 'Please enable location permissions for this app.';
            } else if (error.code === 2) { // POSITION_UNAVAILABLE
                errorMessage += 'Location information is unavailable.';
            } else if (error.code === 3) { // TIMEOUT
                errorMessage += 'Location request timed out.';
            } else {
                errorMessage += 'Please try again.';
            }
            alert(errorMessage);
        }
    }, [user, synth, handleUnlockNode]);

    const closeRewardModal = useCallback(() => {
        setActiveModal(null);
        setReward(null);
    }, []);

    const openCouponModal = useCallback(() => setActiveModal('COUPON'), []);
    const closeCouponModal = useCallback(() => setActiveModal('REWARD'), []);

    const codexSize = "min(80vw, 400px)";

    if (!isInitialized) {
        return <div className="bg-white h-full flex items-center justify-center"><div className="text-gray-600">Loading Theme Journey...</div></div>;
    }

    return (
        <div className="bg-white text-gray-900 h-full font-sans flex flex-col items-center p-4 overflow-y-auto min-h-full">
            <div className="w-full max-w-3xl mx-auto text-center px-4 pb-8 relative">
                {backgroundImage && <img src={backgroundImage} alt="Journey background" className="absolute inset-0 w-full h-full object-cover opacity-10 -z-10" />}
                <button onClick={onBack} className="absolute top-4 left-0 bg-gray-200 text-gray-700 p-2 rounded-full hover:bg-gray-300 transition-colors z-10">
                    &larr; Back
                </button>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-800 tracking-wider mt-16 mb-4">{title || 'Theme Journey'}</h1>
                {description && (
                    <div className="text-base md:text-lg text-gray-700 leading-relaxed mt-4">
                        <div dangerouslySetInnerHTML={{ __html: isDescriptionExpanded ? description : `${description.substring(0, 100)}...` }} />
                        <button onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)} className="text-sky-500 hover:underline">
                            {isDescriptionExpanded ? 'Read Less' : 'Read More'}
                        </button>
                    </div>
                )}
            </div>

            <div className="relative flex items-center justify-center my-8" style={{ width: codexSize, height: codexSize }}>
                <img 
                    src={codexData?.background_image || "https://placehold.co/400x400/022c43/9af1ff?text=Codex"}
                    alt="Codex background"
                    className="absolute inset-0 w-full h-full rounded-full opacity-10"
                />
                
                {nodes.map((node) => (
                    <div key={node.id} className="absolute inset-0 w-full h-full">
                        <AnimatePresence>
                            {node.unlocked && (
                                <motion.div
                                    className="absolute inset-0 w-full h-full"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 1.5 }}
                                >
                                    <img 
                                        src={codexData?.glyph_image || "https://placehold.co/400x400/022c43/9af1ff?text=Codex"}
                                        alt={`Piece ${node.id}`}
                                        className="w-full h-full"
                                        style={{ clipPath: node.clipPath }}
                                    />
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                ))}

                {nodes.map((node, index) => {
                    const angle = (index / nodes.length) * (2 * Math.PI) - (Math.PI / 2);
                    const x = `calc(50% + ${Math.cos(angle) * 45}% - 16px)`;
                    const y = `calc(50% + ${Math.sin(angle) * 45}% - 16px)`;
                    return (
                        <motion.button
                            key={`button-${node.id}`}
                            onClick={() => setSelectedNode(node)}
                            className="absolute w-10 h-10 md:w-8 md:h-8 rounded-full flex items-center justify-center transition-all duration-500 shadow-lg"
                            style={{ top: y, left: x }}
                            whileHover={{ scale: 1.2 }}
                            animate={{
                                backgroundColor: node.unlocked ? 'rgba(0, 255, 255, 0.4)' : 'rgba(209, 213, 219, 0.7)',
                                border: node.unlocked ? '2px solid rgba(0, 255, 255, 0.8)' : '2px solid rgba(156, 163, 175, 0.8)',
                                boxShadow: node.unlocked ? '0 0 15px rgba(0, 255, 255, 0.3)' : '0 4px 8px rgba(0, 0, 0, 0.2)',
                            }}
                        >
                            {node.unlocked ? (
                                <img src={node.glyph} alt="glyph" className="w-6 h-6 md:w-5 md:h-5" />
                            ) : (
                                <div className="w-6 h-6 md:w-5 md:h-5">
                                    <LockIcon />
                                </div>
                            )}
                        </motion.button>
                    );
                })}
            </div>
            
            <div className="text-center mt-2 max-w-md">
                <p className="text-gray-600 text-sm md:text-base">
                    {unlockedCount} of {nodes.length || 0} nodes unlocked. Tap a node to explore.
                </p>
            </div>

            <AnimatePresence>
                {selectedNode && !activeModal && !revealedNode && (
                    <NodeDetailModal
                        node={selectedNode}
                        onUnlock={handleLocationUnlock}
                        onClose={() => setSelectedNode(null)}
                    />
                )}
                {activeModal === 'REWARD' && reward && (
                    <RewardModal
                        reward={reward}
                        onClose={closeRewardModal}
                        onOpenCoupon={openCouponModal}
                    />
                )}
                {activeModal === 'COUPON' && reward && (
                    <CouponModal
                        reward={reward}
                        user={user}
                        journeyId={journeyId}
                        unlockedCount={unlockedCount}
                        onClose={closeCouponModal}
                    />
                )}
                {revealedNode && !activeModal && !selectedNode && (
                    <RevealModal
                        node={revealedNode}
                        codexImage={codexData?.glyph_image}
                    />
                )}
            </AnimatePresence>
        </div>
    );
}

// --- Main App Component ---

import JourneySelector from './JourneySelector';

export default function Codex() {
    const [currentUser, setCurrentUser] = useState(null);
    const [journeyId, setJourneyId] = useState(null);
    const [journeyManuallySelected, setJourneyManuallySelected] = useState(false);
    const [availableJourneys, setAvailableJourneys] = useState([]);
    const [availableRegions, setAvailableRegions] = useState([]);
    const [isInitialized, setIsInitialized] = useState(false);
    const [componentKey, setComponentKey] = useState(0);
    const [error, setError] = useState(null);

    const fetchAvailableJourneys = async () => {
        try {
            setError(null);
            const { db, collection, getDocs } = await getFirebase();
            const codexesRef = collection(db, 'codexes');
            const snapshot = await getDocs(codexesRef);
            const journeys = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            setAvailableJourneys(journeys);

            // Extract unique regions
            const regions = [...new Set(journeys.map(j => j.region).filter(Boolean))];
            setAvailableRegions(regions);

            return journeys;
        } catch (error) {
            console.error("Error fetching available journeys:", error);
            setError("Failed to load journeys. Please check your connection.");
            return [];
        }
    };

    const handleJourneySelect = (newJourneyId) => {
        try {
            if (!newJourneyId) return;
            
            setJourneyId(newJourneyId);
            setJourneyManuallySelected(true);
            setComponentKey(prev => prev + 1);

            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('journey', newJourneyId);
            history.pushState({}, '', currentUrl);

            window.dispatchEvent(new CustomEvent('journey-load', { detail: { journeyId: newJourneyId } }));
        } catch (error) {
            console.error("Error changing journey:", error);
            setError("Failed to load journey.");
        }
    };

    const handleBack = () => {
        try {
            // Clear all state immediately
            setJourneyId(null);
            setJourneyManuallySelected(false);
            setComponentKey(prev => prev + 1);
            setError(null);
            
            // Clear URL
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('journey');
            history.pushState({}, '', currentUrl);
            
            // Don't dispatch the event - it causes conflicts
            // window.dispatchEvent(new CustomEvent('journey-load', { detail: { journeyId: null } }));
        } catch (error) {
            console.error("Error going back:", error);
            // Force reload if back fails
            window.location.reload();
        }
    };

    useEffect(() => {
        let mounted = true;
        
        const handleJourneyLoad = (event) => {
            if (!mounted) return;
            setJourneyId(event.detail.journeyId);
            setJourneyManuallySelected(true);
        };
        
        window.addEventListener('journey-load', handleJourneyLoad);

        fetchAvailableJourneys().then(() => {
            if (mounted) setIsInitialized(true);
        });

        return () => {
            mounted = false;
            window.removeEventListener('journey-load', handleJourneyLoad);
        };
    }, []);

    useEffect(() => {
        console.log('🔥 Codex: Setting up Firebase auth listener...');
        let unsubscribe;
        let mounted = true;

        getFirebase().then(({ auth, onAuthStateChanged }) => {
            console.log('✅ Codex: Firebase obtained, setting up auth listener');
            if (!mounted) return;
            unsubscribe = onAuthStateChanged(auth, (user) => {
                console.log('👤 Codex: Auth state changed:', !!user);
                if (!mounted) return;
                setCurrentUser(user || null);
                if (!user && mounted) setIsInitialized(true);
            });
        }).catch(error => {
            console.error("❌ Codex: Auth listener failed:", error);
            if (mounted) {
                setError("Authentication failed. Please refresh the page.");
                setIsInitialized(true);
            }
        });

        return () => {
            console.log('🧹 Codex: Cleaning up auth listener');
            mounted = false;
            if (unsubscribe) unsubscribe();
        };
    }, []);

    if (error) {
        return (
            <div className="bg-white h-full flex items-center justify-center p-4">
                <div className="text-center">
                    <h2 className="text-xl font-bold text-red-600 mb-4">Error</h2>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <button 
                        onClick={() => window.location.reload()}
                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                        Reload Page
                    </button>
                </div>
            </div>
        );
    }

    if (!isInitialized) {
        return (
            <div className="bg-white h-full flex items-center justify-center">
                <div className="text-gray-600">Loading...</div>
            </div>
        );
    }

    // Use React.Fragment with key to prevent DOM issues
    return (
        <React.Fragment key={`codex-root-${componentKey}`}>
            {!journeyManuallySelected || !journeyId ? (
                <JourneySelector
                    journeys={availableJourneys}
                    regions={availableRegions}
                    onJourneySelect={handleJourneySelect}
                />
            ) : (
                <React.Fragment key={`journey-container-${journeyId}-${componentKey}`}>
                    {(() => {
                        const selectedJourney = availableJourneys.find(j => j.id === journeyId);
                        
                        if (selectedJourney?.type === 'mystery') {
                            return <MysteryJourney
                                key={`mystery-${journeyId}-${componentKey}`}
                                journeyId={journeyId}
                                user={currentUser}
                                title={selectedJourney.title}
                                description={selectedJourney.description}
                                backgroundImage={selectedJourney.background_image}
                                onBack={handleBack}
                            />
                        } else {
                            return <ThemeJourney
                                key={`theme-${journeyId}-${componentKey}`}
                                journeyId={journeyId}
                                user={currentUser}
                                title={selectedJourney?.title}
                                description={selectedJourney?.description}
                                backgroundImage={selectedJourney?.background_image}
                                onBack={handleBack}
                            />
                        }
                    })()}
                </React.Fragment>
            )}
        </React.Fragment>
    );
}
