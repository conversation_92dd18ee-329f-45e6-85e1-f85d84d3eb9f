/* Note: Most styling is handled by Tailwind CSS classes in the HTML file. 
    This file is for custom styles and animations that are not part of Tailwind. */

html, body {
    margin: 0; /* Remove default margin */
    font-family: 'Inter', sans-serif;
    background-color: #f1f5f9; /* Replicates bg-slate-100 from body tag */
}

#chat-messages::-webkit-scrollbar,
#photo-gallery::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track,
#photo-gallery::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
    background: #f1f5f9;
}

#chat-messages::-webkit-scrollbar-thumb,
#photo-gallery::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
    background: #94a3b8;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb:hover,
#photo-gallery::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
    background: #475569;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.message-bubble {
    max-width: 80%;
    word-wrap: break-word;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
    100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

#home-bg {
    background-image: url('../assets/mountain.webp');
}

/* --- Home Screen Responsive Buttons --- */
#home-button-container {
    /* Remove all positioning - let flexbox handle it */
    filter: drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04)) drop-shadow(0 4px 3px rgba(0, 0, 0, 0.1));
}

/* Remove triangle background styles - no longer needed */
#home-triangle-bg {
    display: none !important; /* Hide the triangle background */
}

#home-sos-button {
    font-size: 1.25rem !important; /* Fixed font size */
    position: static !important; /* Remove absolute positioning */
}

#share-location-button-home,
#sound-scape-button {
    font-size: 0.875rem !important; /* Fixed font size */
    position: static !important; /* Remove absolute positioning */
}

/* Add a media query for larger screens */
@media (min-width: 640px) {
    #home-sos-button {
        width: 100px !important;
        height: 100px !important;
        font-size: 1.5rem !important;
    }
    #share-location-button-home,
    #sound-scape-button {
        width: 100px !important;
        height: 100px !important;
        font-size: 1rem !important;
    }
}

.gradient-button {
    background-image: linear-gradient(to right, #0ea5e9, #6366f1, #2563eb);
    animation: gradient-animation 3s ease infinite;
    background-size: 200% 200%;
}

@keyframes gradient-animation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.main-nav {
    /* Provide a base padding and add the safe-area inset, which is 0 on desktop */
    padding-bottom: env(safe-area-inset-bottom);
}

/* Codex specific safe area handling */
#codex-screen {
    /* Ensure codex content respects safe areas and doesn't scroll */
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    background-color: white;
}

#codex-root {
    /* Full height within the scrollable content area */
    height: 100%;
}

/* Mobile-specific improvements for codex */
@media (max-width: 768px) {
    #codex-root {
        /* Reduce bottom padding to eliminate white space */
        padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    }
}