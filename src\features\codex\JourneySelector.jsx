import React, { useState } from 'react';
import { motion } from 'framer-motion';

const JourneyCard = ({ journey, onSelect }) => (
    <motion.div
        layout
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="relative rounded-lg overflow-hidden shadow-lg cursor-pointer group"
        onClick={() => onSelect(journey.id)}
    >
        <img 
            src={journey.featured_image_url || 'https://placehold.co/600x400/022c43/9af1ff?text=Journey'} 
            alt={journey.title} 
            className="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-300"
        />
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="absolute bottom-0 left-0 p-4">
            <h3 className="text-white text-xl font-bold">{journey.title}</h3>
            <p className="text-gray-300">{journey.type === 'mystery' ? 'Mystery Journey' : 'Theme Journey'}</p>
        </div>
    </motion.div>
);

const JourneySelector = ({ journeys, onJourneySelect, regions }) => {
    const [selectedRegion, setSelectedRegion] = useState('All');

    const handleHowToUse = (e) => {
        e.preventDefault();
        // Navigate to the info screen
        const navInfo = document.getElementById('nav-info');
        if (navInfo) navInfo.click();

        // Show the user guide
        setTimeout(() => {
            const userGuideButton = document.getElementById('info-user-guide-button');
            if (userGuideButton) userGuideButton.click();

            // Open the specific guide section
            setTimeout(() => {
                const allDetails = document.querySelectorAll('#user-guide-screen details');
                allDetails.forEach(details => {
                    details.open = false;
                });
                const guideSection = document.getElementById('guide-journeys');
                if (guideSection) {
                    guideSection.open = true;
                    guideSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 100);
        }, 100);
    };

    const abbreviateRegion = (region) => {
        const abbreviations = {
            'South Australia': 'SA',
            'Victoria': 'Vic',
        };
        return abbreviations[region] || region;
    };

    const filteredJourneys = selectedRegion === 'All'
        ? journeys
        : journeys.filter(j => j.region === selectedRegion);

    return (
        <div className="bg-gray-100 text-gray-900 h-full font-sans p-4 sm:p-6 md:p-8">
            <div className="max-w-7xl mx-auto">
                <header className="text-center mb-6 pt-6">
                    <h1 className="text-4xl md:text-5xl font-medium text-gray-700 tracking-tight">Journeys</h1>
                    <p className="text-lg text-gray-600 mt-2"><a href="#" onClick={handleHowToUse} className="text-sky-500 hover:underline">How to Use</a></p>
                </header>

                <div className="flex justify-center flex-wrap gap-2 mb-8">
                    <button 
                        onClick={() => setSelectedRegion('All')}
                        className={`px-4 py-2 rounded-full font-medium transition-colors ${selectedRegion === 'All' ? 'bg-cyan-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-200'}`}
                    >
                        All
                    </button>
                    {regions.map(region => (
                        <button 
                            key={region}
                            onClick={() => setSelectedRegion(region)}
                            className={`px-4 py-2 rounded-full font-medium transition-colors ${selectedRegion === region ? 'bg-cyan-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-200'}`}
                        >
                            {abbreviateRegion(region)}
                        </button>
                    ))}
                </div>

                <motion.div layout className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredJourneys.map(journey => (
                        <JourneyCard key={journey.id} journey={journey} onSelect={onJourneySelect} />
                    ))}
                </motion.div>
            </div>
        </div>
    );
};

export default JourneySelector;